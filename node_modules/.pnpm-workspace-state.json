{"lastValidatedTimestamp": 1750426738647, "projects": {"/Users/<USER>/Desktop/ALLLearn/Project/VueUseProject": {"name": "vue_use_project", "version": "1.0.0"}, "/Users/<USER>/Desktop/ALLLearn/Project/VueUseProject/packages/cores": {"name": "cores", "version": "1.0.0"}}, "pnpmfileExists": false, "settings": {"autoInstallPeers": true, "catalogs": {}, "dedupeDirectDeps": false, "dedupeInjectedDeps": true, "dedupePeerDependents": true, "dev": true, "excludeLinksFromLockfile": false, "hoistPattern": ["*"], "hoistWorkspacePackages": true, "injectWorkspacePackages": false, "linkWorkspacePackages": false, "nodeLinker": "isolated", "optional": true, "preferWorkspacePackages": false, "production": true, "publicHoistPattern": [], "workspacePackagePatterns": ["packages/*", "apps/*"]}, "filteredInstall": true}