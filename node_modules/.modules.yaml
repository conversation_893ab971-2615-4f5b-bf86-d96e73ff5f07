hoistPattern:
  - '*'
hoistedDependencies:
  '@antfu/install-pkg@1.1.0':
    '@antfu/install-pkg': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/parser@7.27.5':
    '@babel/parser': private
  '@babel/types@7.27.6':
    '@babel/types': private
  '@clack/core@0.5.0':
    '@clack/core': private
  '@clack/prompts@0.11.0':
    '@clack/prompts': private
  '@commitlint/config-validator@19.8.1':
    '@commitlint/config-validator': private
  '@commitlint/execute-rule@19.8.1':
    '@commitlint/execute-rule': private
  '@commitlint/load@19.8.1(@types/node@24.0.3)(typescript@5.8.3)':
    '@commitlint/load': private
  '@commitlint/resolve-extends@19.8.1':
    '@commitlint/resolve-extends': private
  '@commitlint/types@19.8.1':
    '@commitlint/types': private
  '@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4)':
    '@csstools/css-parser-algorithms': private
  '@csstools/css-tokenizer@3.0.4':
    '@csstools/css-tokenizer': private
  '@csstools/media-query-list-parser@4.0.3(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)':
    '@csstools/media-query-list-parser': private
  '@csstools/selector-specificity@5.0.0(postcss-selector-parser@7.1.0)':
    '@csstools/selector-specificity': private
  '@dprint/formatter@0.3.0':
    '@dprint/formatter': private
  '@dprint/markdown@0.17.8':
    '@dprint/markdown': private
  '@dprint/toml@0.6.4':
    '@dprint/toml': private
  '@dual-bundle/import-meta-resolve@4.1.0':
    '@dual-bundle/import-meta-resolve': private
  '@es-joy/jsdoccomment@0.50.2':
    '@es-joy/jsdoccomment': private
  '@eslint-community/eslint-plugin-eslint-comments@4.5.0(eslint@9.29.0(jiti@2.4.2))':
    '@eslint-community/eslint-plugin-eslint-comments': private
  '@eslint-community/eslint-utils@4.7.0(eslint@9.29.0(jiti@2.4.2))':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/compat@1.3.0(eslint@9.29.0(jiti@2.4.2))':
    '@eslint/compat': private
  '@eslint/config-array@0.20.1':
    '@eslint/config-array': private
  '@eslint/config-helpers@0.2.3':
    '@eslint/config-helpers': private
  '@eslint/core@0.14.0':
    '@eslint/core': private
  '@eslint/eslintrc@3.3.1':
    '@eslint/eslintrc': private
  '@eslint/js@9.29.0':
    '@eslint/js': private
  '@eslint/markdown@6.5.0':
    '@eslint/markdown': private
  '@eslint/object-schema@2.1.6':
    '@eslint/object-schema': private
  '@eslint/plugin-kit@0.3.2':
    '@eslint/plugin-kit': private
  '@humanfs/core@0.19.1':
    '@humanfs/core': private
  '@humanfs/node@0.16.6':
    '@humanfs/node': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/retry@0.4.3':
    '@humanwhocodes/retry': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@keyv/serialize@1.0.3':
    '@keyv/serialize': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@pkgr/core@0.1.2':
    '@pkgr/core': private
  '@rollup/rollup-android-arm-eabi@4.44.0':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.44.0':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.44.0':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.44.0':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.44.0':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.44.0':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.44.0':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.44.0':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.44.0':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.44.0':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.44.0':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-powerpc64le-gnu@4.44.0':
    '@rollup/rollup-linux-powerpc64le-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.44.0':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.44.0':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.44.0':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.44.0':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.44.0':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.44.0':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.44.0':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.44.0':
    '@rollup/rollup-win32-x64-msvc': private
  '@stylistic/eslint-plugin@5.0.0-beta.5(eslint@9.29.0(jiti@2.4.2))':
    '@stylistic/eslint-plugin': private
  '@types/conventional-commits-parser@5.0.1':
    '@types/conventional-commits-parser': private
  '@types/debug@4.1.12':
    '@types/debug': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/mdast@4.0.4':
    '@types/mdast': private
  '@types/ms@2.1.0':
    '@types/ms': private
  '@types/node@24.0.3':
    '@types/node': private
  '@types/unist@3.0.3':
    '@types/unist': private
  '@typescript-eslint/eslint-plugin@8.34.1(@typescript-eslint/parser@8.34.1(eslint@9.29.0(jiti@2.4.2))(typescript@5.8.3))(eslint@9.29.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/eslint-plugin': private
  '@typescript-eslint/parser@8.34.1(eslint@9.29.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/parser': private
  '@typescript-eslint/project-service@8.34.1(typescript@5.8.3)':
    '@typescript-eslint/project-service': private
  '@typescript-eslint/scope-manager@8.34.1':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/tsconfig-utils@8.34.1(typescript@5.8.3)':
    '@typescript-eslint/tsconfig-utils': private
  '@typescript-eslint/type-utils@8.34.1(eslint@9.29.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/type-utils': private
  '@typescript-eslint/types@8.34.1':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@8.34.1(typescript@5.8.3)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@8.34.1(eslint@9.29.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@8.34.1':
    '@typescript-eslint/visitor-keys': private
  '@vitest/eslint-plugin@1.2.7(eslint@9.29.0(jiti@2.4.2))(typescript@5.8.3)':
    '@vitest/eslint-plugin': private
  '@vue/compiler-core@3.5.17':
    '@vue/compiler-core': private
  '@vue/compiler-dom@3.5.17':
    '@vue/compiler-dom': private
  '@vue/compiler-sfc@3.5.17':
    '@vue/compiler-sfc': private
  '@vue/compiler-ssr@3.5.17':
    '@vue/compiler-ssr': private
  '@vue/shared@3.5.17':
    '@vue/shared': private
  acorn-jsx@5.3.2(acorn@8.15.0):
    acorn-jsx: private
  acorn@8.15.0:
    acorn: private
  ajv@6.12.6:
    ajv: private
  ansi-escapes@4.3.2:
    ansi-escapes: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  ansis@4.1.0:
    ansis: private
  are-docs-informative@0.0.2:
    are-docs-informative: private
  argparse@2.0.1:
    argparse: private
  array-union@2.1.0:
    array-union: private
  astral-regex@2.0.0:
    astral-regex: private
  at-least-node@1.0.0:
    at-least-node: private
  balanced-match@2.0.0:
    balanced-match: private
  base64-js@1.5.1:
    base64-js: private
  bl@4.1.0:
    bl: private
  boolbase@1.0.0:
    boolbase: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.25.0:
    browserslist: private
  buffer@5.7.1:
    buffer: private
  builtin-modules@5.0.0:
    builtin-modules: private
  cac@6.7.14:
    cac: private
  cacheable@1.10.0:
    cacheable: private
  cachedir@2.3.0:
    cachedir: private
  callsites@3.1.0:
    callsites: private
  caniuse-lite@1.0.30001724:
    caniuse-lite: private
  ccount@2.0.1:
    ccount: private
  chalk@4.1.2:
    chalk: private
  character-entities@2.0.2:
    character-entities: private
  chardet@0.7.0:
    chardet: private
  ci-info@4.2.0:
    ci-info: private
  clean-regexp@1.0.0:
    clean-regexp: private
  cli-cursor@3.1.0:
    cli-cursor: private
  cli-spinners@2.9.2:
    cli-spinners: private
  cli-truncate@4.0.0:
    cli-truncate: private
  cli-width@3.0.0:
    cli-width: private
  clone@1.0.4:
    clone: private
  color-convert@1.9.3:
    color-convert: private
  color-name@1.1.3:
    color-name: private
  colord@2.9.3:
    colord: private
  colorette@2.0.20:
    colorette: private
  commander@14.0.0:
    commander: private
  comment-parser@1.4.1:
    comment-parser: private
  concat-map@0.0.1:
    concat-map: private
  confbox@0.2.2:
    confbox: private
  conventional-commit-types@3.0.0:
    conventional-commit-types: private
  core-js-compat@3.43.0:
    core-js-compat: private
  cosmiconfig-typescript-loader@6.1.0(@types/node@24.0.3)(cosmiconfig@9.0.0(typescript@5.8.3))(typescript@5.8.3):
    cosmiconfig-typescript-loader: private
  cosmiconfig@9.0.0(typescript@5.8.3):
    cosmiconfig: private
  cross-spawn@7.0.6:
    cross-spawn: private
  css-functions-list@3.2.3:
    css-functions-list: private
  css-tree@3.1.0:
    css-tree: private
  cssesc@3.0.0:
    cssesc: private
  cz-conventional-changelog@3.3.0(@types/node@24.0.3)(typescript@5.8.3):
    cz-conventional-changelog: private
  debug@4.4.1:
    debug: private
  decode-named-character-reference@1.2.0:
    decode-named-character-reference: private
  dedent@0.7.0:
    dedent: private
  deep-is@0.1.4:
    deep-is: private
  defaults@1.0.4:
    defaults: private
  dequal@2.0.3:
    dequal: private
  detect-file@1.0.0:
    detect-file: private
  detect-indent@6.1.0:
    detect-indent: private
  devlop@1.1.0:
    devlop: private
  dir-glob@3.0.1:
    dir-glob: private
  dom-serializer@2.0.0:
    dom-serializer: private
  domelementtype@2.3.0:
    domelementtype: private
  domhandler@5.0.3:
    domhandler: private
  domutils@3.2.2:
    domutils: private
  electron-to-chromium@1.5.171:
    electron-to-chromium: private
  emoji-regex@8.0.0:
    emoji-regex: private
  enhanced-resolve@5.18.1:
    enhanced-resolve: private
  entities@4.5.0:
    entities: private
  env-paths@2.2.1:
    env-paths: private
  environment@1.1.0:
    environment: private
  error-ex@1.3.2:
    error-ex: private
  escalade@3.2.0:
    escalade: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-compat-utils@0.6.5(eslint@9.29.0(jiti@2.4.2)):
    eslint-compat-utils: private
  eslint-config-flat-gitignore@2.1.0(eslint@9.29.0(jiti@2.4.2)):
    eslint-config-flat-gitignore: private
  eslint-flat-config-utils@2.1.0:
    eslint-flat-config-utils: private
  eslint-formatting-reporter@0.0.0(eslint@9.29.0(jiti@2.4.2)):
    eslint-formatting-reporter: private
  eslint-json-compat-utils@0.2.1(eslint@9.29.0(jiti@2.4.2))(jsonc-eslint-parser@2.4.0):
    eslint-json-compat-utils: private
  eslint-merge-processors@2.0.0(eslint@9.29.0(jiti@2.4.2)):
    eslint-merge-processors: private
  eslint-parser-plain@0.1.1:
    eslint-parser-plain: private
  eslint-plugin-antfu@3.1.1(eslint@9.29.0(jiti@2.4.2)):
    eslint-plugin-antfu: private
  eslint-plugin-command@3.3.0(eslint@9.29.0(jiti@2.4.2)):
    eslint-plugin-command: private
  eslint-plugin-es-x@7.8.0(eslint@9.29.0(jiti@2.4.2)):
    eslint-plugin-es-x: private
  eslint-plugin-import-lite@0.3.0(eslint@9.29.0(jiti@2.4.2))(typescript@5.8.3):
    eslint-plugin-import-lite: private
  eslint-plugin-jsdoc@51.0.3(eslint@9.29.0(jiti@2.4.2)):
    eslint-plugin-jsdoc: private
  eslint-plugin-jsonc@2.20.1(eslint@9.29.0(jiti@2.4.2)):
    eslint-plugin-jsonc: private
  eslint-plugin-n@17.20.0(eslint@9.29.0(jiti@2.4.2))(typescript@5.8.3):
    eslint-plugin-n: private
  eslint-plugin-no-only-tests@3.3.0:
    eslint-plugin-no-only-tests: private
  eslint-plugin-perfectionist@4.15.0(eslint@9.29.0(jiti@2.4.2))(typescript@5.8.3):
    eslint-plugin-perfectionist: private
  eslint-plugin-pnpm@0.3.1(eslint@9.29.0(jiti@2.4.2)):
    eslint-plugin-pnpm: private
  eslint-plugin-regexp@2.9.0(eslint@9.29.0(jiti@2.4.2)):
    eslint-plugin-regexp: private
  eslint-plugin-toml@0.12.0(eslint@9.29.0(jiti@2.4.2)):
    eslint-plugin-toml: private
  eslint-plugin-unicorn@59.0.1(eslint@9.29.0(jiti@2.4.2)):
    eslint-plugin-unicorn: private
  eslint-plugin-unused-imports@4.1.4(@typescript-eslint/eslint-plugin@8.34.1(@typescript-eslint/parser@8.34.1(eslint@9.29.0(jiti@2.4.2))(typescript@5.8.3))(eslint@9.29.0(jiti@2.4.2))(typescript@5.8.3))(eslint@9.29.0(jiti@2.4.2)):
    eslint-plugin-unused-imports: private
  eslint-plugin-vue@10.2.0(eslint@9.29.0(jiti@2.4.2))(vue-eslint-parser@10.1.3(eslint@9.29.0(jiti@2.4.2))):
    eslint-plugin-vue: private
  eslint-plugin-yml@1.18.0(eslint@9.29.0(jiti@2.4.2)):
    eslint-plugin-yml: private
  eslint-processor-vue-blocks@2.0.0(@vue/compiler-sfc@3.5.17)(eslint@9.29.0(jiti@2.4.2)):
    eslint-processor-vue-blocks: private
  eslint-scope@8.4.0:
    eslint-scope: private
  eslint-visitor-keys@4.2.1:
    eslint-visitor-keys: private
  espree@10.4.0:
    espree: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  estree-walker@2.0.2:
    estree-walker: private
  esutils@2.0.3:
    esutils: private
  eventemitter3@5.0.1:
    eventemitter3: private
  expand-tilde@2.0.2:
    expand-tilde: private
  exsolve@1.0.7:
    exsolve: private
  external-editor@3.1.0:
    external-editor: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-diff@1.3.0:
    fast-diff: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fast-uri@3.0.6:
    fast-uri: private
  fastest-levenshtein@1.0.16:
    fastest-levenshtein: private
  fastq@1.19.1:
    fastq: private
  fault@2.0.1:
    fault: private
  fdir@6.4.6(picomatch@4.0.2):
    fdir: private
  figures@3.2.0:
    figures: private
  file-entry-cache@8.0.0:
    file-entry-cache: private
  fill-range@7.1.1:
    fill-range: private
  find-node-modules@2.1.3:
    find-node-modules: private
  find-root@1.1.0:
    find-root: private
  find-up-simple@1.0.1:
    find-up-simple: private
  find-up@5.0.0:
    find-up: private
  findup-sync@4.0.0:
    findup-sync: private
  flat-cache@6.1.10:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  format@0.2.2:
    format: private
  fs-extra@9.1.0:
    fs-extra: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  get-east-asian-width@1.3.0:
    get-east-asian-width: private
  get-tsconfig@4.10.1:
    get-tsconfig: private
  glob-parent@6.0.2:
    glob-parent: private
  glob@7.2.3:
    glob: private
  global-directory@4.0.1:
    global-directory: private
  global-modules@2.0.0:
    global-modules: private
  global-prefix@3.0.0:
    global-prefix: private
  globals@16.2.0:
    globals: private
  globby@11.1.0:
    globby: private
  globjoin@0.1.4:
    globjoin: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  has-flag@4.0.0:
    has-flag: private
  homedir-polyfill@1.0.3:
    homedir-polyfill: private
  hookified@1.9.1:
    hookified: private
  html-tags@3.3.1:
    html-tags: private
  htmlparser2@8.0.2:
    htmlparser2: private
  iconv-lite@0.4.24:
    iconv-lite: private
  ieee754@1.2.1:
    ieee754: private
  ignore@5.3.2:
    ignore: private
  import-fresh@3.3.1:
    import-fresh: private
  import-meta-resolve@4.1.0:
    import-meta-resolve: private
  imurmurhash@0.1.4:
    imurmurhash: private
  indent-string@5.0.0:
    indent-string: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  ini@1.3.8:
    ini: private
  inquirer@8.2.5:
    inquirer: private
  is-arrayish@0.2.1:
    is-arrayish: private
  is-builtin-module@5.0.0:
    is-builtin-module: private
  is-extglob@2.1.1:
    is-extglob: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-glob@4.0.3:
    is-glob: private
  is-interactive@1.0.0:
    is-interactive: private
  is-number@7.0.0:
    is-number: private
  is-plain-object@5.0.0:
    is-plain-object: private
  is-unicode-supported@0.1.0:
    is-unicode-supported: private
  is-utf8@0.2.1:
    is-utf8: private
  is-windows@1.0.2:
    is-windows: private
  isexe@2.0.0:
    isexe: private
  jiti@2.4.2:
    jiti: private
  js-tokens@9.0.1:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsdoc-type-pratt-parser@4.1.0:
    jsdoc-type-pratt-parser: private
  jsesc@3.1.0:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  jsonc-eslint-parser@2.4.0:
    jsonc-eslint-parser: private
  jsonfile@6.1.0:
    jsonfile: private
  keyv@4.5.4:
    keyv: private
  kind-of@6.0.3:
    kind-of: private
  known-css-properties@0.37.0:
    known-css-properties: private
  levn@0.4.1:
    levn: private
  lilconfig@3.1.3:
    lilconfig: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  listr2@8.3.3:
    listr2: private
  local-pkg@1.1.1:
    local-pkg: private
  locate-path@6.0.0:
    locate-path: private
  lodash.isplainobject@4.0.6:
    lodash.isplainobject: private
  lodash.map@4.6.0:
    lodash.map: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash.mergewith@4.6.2:
    lodash.mergewith: private
  lodash.truncate@4.4.2:
    lodash.truncate: private
  lodash.uniq@4.5.0:
    lodash.uniq: private
  lodash@4.17.21:
    lodash: private
  log-symbols@4.1.0:
    log-symbols: private
  log-update@6.1.0:
    log-update: private
  longest-streak@3.1.0:
    longest-streak: private
  longest@2.0.1:
    longest: private
  magic-string@0.30.17:
    magic-string: private
  markdown-table@3.0.4:
    markdown-table: private
  mathml-tag-names@2.1.3:
    mathml-tag-names: private
  mdast-util-find-and-replace@3.0.2:
    mdast-util-find-and-replace: private
  mdast-util-from-markdown@2.0.2:
    mdast-util-from-markdown: private
  mdast-util-frontmatter@2.0.1:
    mdast-util-frontmatter: private
  mdast-util-gfm-autolink-literal@2.0.1:
    mdast-util-gfm-autolink-literal: private
  mdast-util-gfm-footnote@2.1.0:
    mdast-util-gfm-footnote: private
  mdast-util-gfm-strikethrough@2.0.0:
    mdast-util-gfm-strikethrough: private
  mdast-util-gfm-table@2.0.0:
    mdast-util-gfm-table: private
  mdast-util-gfm-task-list-item@2.0.0:
    mdast-util-gfm-task-list-item: private
  mdast-util-gfm@3.1.0:
    mdast-util-gfm: private
  mdast-util-phrasing@4.1.0:
    mdast-util-phrasing: private
  mdast-util-to-markdown@2.1.2:
    mdast-util-to-markdown: private
  mdast-util-to-string@4.0.0:
    mdast-util-to-string: private
  mdn-data@2.12.2:
    mdn-data: private
  meow@13.2.0:
    meow: private
  merge2@1.4.1:
    merge2: private
  merge@2.1.1:
    merge: private
  micromark-core-commonmark@2.0.3:
    micromark-core-commonmark: private
  micromark-extension-frontmatter@2.0.0:
    micromark-extension-frontmatter: private
  micromark-extension-gfm-autolink-literal@2.1.0:
    micromark-extension-gfm-autolink-literal: private
  micromark-extension-gfm-footnote@2.1.0:
    micromark-extension-gfm-footnote: private
  micromark-extension-gfm-strikethrough@2.1.0:
    micromark-extension-gfm-strikethrough: private
  micromark-extension-gfm-table@2.1.1:
    micromark-extension-gfm-table: private
  micromark-extension-gfm-tagfilter@2.0.0:
    micromark-extension-gfm-tagfilter: private
  micromark-extension-gfm-task-list-item@2.1.0:
    micromark-extension-gfm-task-list-item: private
  micromark-extension-gfm@3.0.0:
    micromark-extension-gfm: private
  micromark-factory-destination@2.0.1:
    micromark-factory-destination: private
  micromark-factory-label@2.0.1:
    micromark-factory-label: private
  micromark-factory-space@2.0.1:
    micromark-factory-space: private
  micromark-factory-title@2.0.1:
    micromark-factory-title: private
  micromark-factory-whitespace@2.0.1:
    micromark-factory-whitespace: private
  micromark-util-character@2.1.1:
    micromark-util-character: private
  micromark-util-chunked@2.0.1:
    micromark-util-chunked: private
  micromark-util-classify-character@2.0.1:
    micromark-util-classify-character: private
  micromark-util-combine-extensions@2.0.1:
    micromark-util-combine-extensions: private
  micromark-util-decode-numeric-character-reference@2.0.2:
    micromark-util-decode-numeric-character-reference: private
  micromark-util-decode-string@2.0.1:
    micromark-util-decode-string: private
  micromark-util-encode@2.0.1:
    micromark-util-encode: private
  micromark-util-html-tag-name@2.0.1:
    micromark-util-html-tag-name: private
  micromark-util-normalize-identifier@2.0.1:
    micromark-util-normalize-identifier: private
  micromark-util-resolve-all@2.0.1:
    micromark-util-resolve-all: private
  micromark-util-sanitize-uri@2.0.1:
    micromark-util-sanitize-uri: private
  micromark-util-subtokenize@2.1.0:
    micromark-util-subtokenize: private
  micromark-util-symbol@2.0.1:
    micromark-util-symbol: private
  micromark-util-types@2.0.2:
    micromark-util-types: private
  micromark@4.0.2:
    micromark: private
  micromatch@4.0.8:
    micromatch: private
  mimic-fn@2.1.0:
    mimic-fn: private
  mimic-function@5.0.1:
    mimic-function: private
  min-indent@1.0.1:
    min-indent: private
  minimatch@3.1.2:
    minimatch: private
  minimist@1.2.7:
    minimist: private
  mlly@1.7.4:
    mlly: private
  ms@2.1.3:
    ms: private
  mute-stream@0.0.8:
    mute-stream: private
  nano-spawn@1.0.2:
    nano-spawn: private
  nanoid@3.3.11:
    nanoid: private
  natural-compare@1.4.0:
    natural-compare: private
  natural-orderby@5.0.0:
    natural-orderby: private
  node-releases@2.0.19:
    node-releases: private
  normalize-path@3.0.0:
    normalize-path: private
  nth-check@2.1.1:
    nth-check: private
  once@1.4.0:
    once: private
  onetime@5.1.2:
    onetime: private
  optionator@0.9.4:
    optionator: private
  ora@5.4.1:
    ora: private
  os-tmpdir@1.0.2:
    os-tmpdir: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  package-manager-detector@1.3.0:
    package-manager-detector: private
  packages/cores:
    cores: private
  parent-module@1.0.1:
    parent-module: private
  parse-gitignore@2.0.0:
    parse-gitignore: private
  parse-imports-exports@0.2.4:
    parse-imports-exports: private
  parse-json@5.2.0:
    parse-json: private
  parse-passwd@1.0.0:
    parse-passwd: private
  parse-statements@1.0.11:
    parse-statements: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-type@4.0.0:
    path-type: private
  pathe@2.0.3:
    pathe: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.2:
    picomatch: private
  pidtree@0.6.0:
    pidtree: private
  pkg-types@2.1.0:
    pkg-types: private
  pluralize@8.0.0:
    pluralize: private
  pnpm-workspace-yaml@0.3.1:
    pnpm-workspace-yaml: private
  postcss-resolve-nested-selector@0.1.6:
    postcss-resolve-nested-selector: private
  postcss-safe-parser@6.0.0(postcss@8.5.6):
    postcss-safe-parser: private
  postcss-selector-parser@7.1.0:
    postcss-selector-parser: private
  postcss-sorting@9.1.0(postcss@8.5.6):
    postcss-sorting: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  postcss@8.5.6:
    postcss: private
  prelude-ls@1.2.1:
    prelude-ls: private
  prettier-linter-helpers@1.0.0:
    prettier-linter-helpers: private
  prettier@3.5.3:
    prettier: private
  punycode@2.3.1:
    punycode: private
  quansync@0.2.10:
    quansync: private
  queue-microtask@1.2.3:
    queue-microtask: private
  readable-stream@3.6.2:
    readable-stream: private
  refa@0.12.1:
    refa: private
  regexp-ast-analysis@0.7.1:
    regexp-ast-analysis: private
  regexp-tree@0.1.27:
    regexp-tree: private
  regjsparser@0.12.0:
    regjsparser: private
  require-from-string@2.0.2:
    require-from-string: private
  resolve-dir@1.0.1:
    resolve-dir: private
  resolve-from@5.0.0:
    resolve-from: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  restore-cursor@3.1.0:
    restore-cursor: private
  reusify@1.1.0:
    reusify: private
  rfdc@1.4.1:
    rfdc: private
  rollup@4.44.0:
    rollup: private
  run-async@2.4.1:
    run-async: private
  run-parallel@1.2.0:
    run-parallel: private
  rxjs@7.8.2:
    rxjs: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safer-buffer@2.1.2:
    safer-buffer: private
  scslre@0.3.0:
    scslre: private
  semver@7.7.2:
    semver: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  signal-exit@4.1.0:
    signal-exit: private
  sisteransi@1.0.5:
    sisteransi: private
  slash@3.0.0:
    slash: private
  slice-ansi@4.0.0:
    slice-ansi: private
  source-map-js@1.2.1:
    source-map-js: private
  spdx-exceptions@2.5.0:
    spdx-exceptions: private
  spdx-expression-parse@4.0.0:
    spdx-expression-parse: private
  spdx-license-ids@3.0.21:
    spdx-license-ids: private
  string-argv@0.3.2:
    string-argv: private
  string-width@4.2.3:
    string-width: private
  string_decoder@1.3.0:
    string_decoder: private
  strip-ansi@6.0.1:
    strip-ansi: private
  strip-bom@4.0.0:
    strip-bom: private
  strip-indent@4.0.0:
    strip-indent: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  stylelint-config-recommended-less@3.0.1(postcss@8.5.6)(stylelint@16.21.0(typescript@5.8.3)):
    stylelint-config-recommended-less: private
  stylelint-config-recommended@16.0.0(stylelint@16.21.0(typescript@5.8.3)):
    stylelint-config-recommended: private
  supports-color@7.2.0:
    supports-color: private
  supports-hyperlinks@3.2.0:
    supports-hyperlinks: private
  svg-tags@1.0.0:
    svg-tags: private
  synckit@0.9.3:
    synckit: private
  table@6.9.0:
    table: private
  tapable@2.2.2:
    tapable: private
  through@2.3.8:
    through: private
  tinyexec@1.0.1:
    tinyexec: private
  tinyglobby@0.2.14:
    tinyglobby: private
  tmp@0.0.33:
    tmp: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toml-eslint-parser@0.10.0:
    toml-eslint-parser: private
  ts-api-utils@2.1.0(typescript@5.8.3):
    ts-api-utils: private
  ts-declaration-location@1.0.7(typescript@5.8.3):
    ts-declaration-location: private
  tslib@2.8.1:
    tslib: private
  type-check@0.4.0:
    type-check: private
  type-fest@0.21.3:
    type-fest: private
  ufo@1.6.1:
    ufo: private
  undici-types@7.8.0:
    undici-types: private
  unist-util-is@6.0.0:
    unist-util-is: private
  unist-util-stringify-position@4.0.0:
    unist-util-stringify-position: private
  unist-util-visit-parents@6.0.1:
    unist-util-visit-parents: private
  unist-util-visit@5.0.0:
    unist-util-visit: private
  universalify@2.0.1:
    universalify: private
  update-browserslist-db@1.1.3(browserslist@4.25.0):
    update-browserslist-db: private
  uri-js@4.4.1:
    uri-js: private
  util-deprecate@1.0.2:
    util-deprecate: private
  vue-eslint-parser@10.1.3(eslint@9.29.0(jiti@2.4.2)):
    vue-eslint-parser: private
  wcwidth@1.0.1:
    wcwidth: private
  which@2.0.2:
    which: private
  word-wrap@1.2.5:
    word-wrap: private
  wrap-ansi@7.0.0:
    wrap-ansi: private
  wrappy@1.0.2:
    wrappy: private
  write-file-atomic@5.0.1:
    write-file-atomic: private
  xml-name-validator@4.0.0:
    xml-name-validator: private
  yaml-eslint-parser@1.3.0:
    yaml-eslint-parser: private
  yaml@2.8.0:
    yaml: private
  yocto-queue@0.1.0:
    yocto-queue: private
  zwitch@2.0.4:
    zwitch: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.12.1
pendingBuilds: []
prunedAt: Fri, 20 Jun 2025 12:50:00 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmmirror.com/
skipped:
  - '@rollup/rollup-android-arm-eabi@4.44.0'
  - '@rollup/rollup-android-arm64@4.44.0'
  - '@rollup/rollup-darwin-x64@4.44.0'
  - '@rollup/rollup-freebsd-arm64@4.44.0'
  - '@rollup/rollup-freebsd-x64@4.44.0'
  - '@rollup/rollup-linux-arm-gnueabihf@4.44.0'
  - '@rollup/rollup-linux-arm-musleabihf@4.44.0'
  - '@rollup/rollup-linux-arm64-gnu@4.44.0'
  - '@rollup/rollup-linux-arm64-musl@4.44.0'
  - '@rollup/rollup-linux-loongarch64-gnu@4.44.0'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.44.0'
  - '@rollup/rollup-linux-riscv64-gnu@4.44.0'
  - '@rollup/rollup-linux-riscv64-musl@4.44.0'
  - '@rollup/rollup-linux-s390x-gnu@4.44.0'
  - '@rollup/rollup-linux-x64-gnu@4.44.0'
  - '@rollup/rollup-linux-x64-musl@4.44.0'
  - '@rollup/rollup-win32-arm64-msvc@4.44.0'
  - '@rollup/rollup-win32-ia32-msvc@4.44.0'
  - '@rollup/rollup-win32-x64-msvc@4.44.0'
storeDir: /Users/<USER>/Library/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
