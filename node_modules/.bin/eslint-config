#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Desktop/ALLLearn/Project/VueUseProject/node_modules/.pnpm/@antfu+eslint-config@4.15.0_@vue+compiler-sfc@3.5.17_eslint-plugin-format@1.0.1_eslint@_f09158a34bd2f2fd8d43789d8f6253dd/node_modules/@antfu/eslint-config/bin/node_modules:/Users/<USER>/Desktop/ALLLearn/Project/VueUseProject/node_modules/.pnpm/@antfu+eslint-config@4.15.0_@vue+compiler-sfc@3.5.17_eslint-plugin-format@1.0.1_eslint@_f09158a34bd2f2fd8d43789d8f6253dd/node_modules/@antfu/eslint-config/node_modules:/Users/<USER>/Desktop/ALLLearn/Project/VueUseProject/node_modules/.pnpm/@antfu+eslint-config@4.15.0_@vue+compiler-sfc@3.5.17_eslint-plugin-format@1.0.1_eslint@_f09158a34bd2f2fd8d43789d8f6253dd/node_modules/@antfu/node_modules:/Users/<USER>/Desktop/ALLLearn/Project/VueUseProject/node_modules/.pnpm/@antfu+eslint-config@4.15.0_@vue+compiler-sfc@3.5.17_eslint-plugin-format@1.0.1_eslint@_f09158a34bd2f2fd8d43789d8f6253dd/node_modules:/Users/<USER>/Desktop/ALLLearn/Project/VueUseProject/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Desktop/ALLLearn/Project/VueUseProject/node_modules/.pnpm/@antfu+eslint-config@4.15.0_@vue+compiler-sfc@3.5.17_eslint-plugin-format@1.0.1_eslint@_f09158a34bd2f2fd8d43789d8f6253dd/node_modules/@antfu/eslint-config/bin/node_modules:/Users/<USER>/Desktop/ALLLearn/Project/VueUseProject/node_modules/.pnpm/@antfu+eslint-config@4.15.0_@vue+compiler-sfc@3.5.17_eslint-plugin-format@1.0.1_eslint@_f09158a34bd2f2fd8d43789d8f6253dd/node_modules/@antfu/eslint-config/node_modules:/Users/<USER>/Desktop/ALLLearn/Project/VueUseProject/node_modules/.pnpm/@antfu+eslint-config@4.15.0_@vue+compiler-sfc@3.5.17_eslint-plugin-format@1.0.1_eslint@_f09158a34bd2f2fd8d43789d8f6253dd/node_modules/@antfu/node_modules:/Users/<USER>/Desktop/ALLLearn/Project/VueUseProject/node_modules/.pnpm/@antfu+eslint-config@4.15.0_@vue+compiler-sfc@3.5.17_eslint-plugin-format@1.0.1_eslint@_f09158a34bd2f2fd8d43789d8f6253dd/node_modules:/Users/<USER>/Desktop/ALLLearn/Project/VueUseProject/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@antfu/eslint-config/bin/index.js" "$@"
else
  exec node  "$basedir/../@antfu/eslint-config/bin/index.js" "$@"
fi
