// 使用示例 - 展示如何使用打包后的库
import {
  createArray,
  debounce,
  DEFAULT_CONFIG,
  fetchUser,
  hello,
  Logger,
  Status,
  version,
} from './dist/index.esm.js'

console.log('=== Cores Library Example ===')
console.log(`Version: ${version}`)

// 基础函数使用
console.log('\n1. 基础函数:')
console.log(hello('World'))
console.log(hello('TypeScript'))

// 泛型函数使用
console.log('\n2. 泛型函数:')
const numbers = createArray(5, 42)
const strings = createArray(3, 'hello')
console.log('Numbers array:', numbers)
console.log('Strings array:', strings)

// 异步函数使用
console.log('\n3. 异步函数:')
fetchUser(1).then((user) => {
  console.log('Fetched user:', user)
})

// 类使用
console.log('\n4. Logger 类:')
const logger = new Logger('[DEMO]')
logger.info('This is an info message')
logger.warn('This is a warning message')
logger.error('This is an error message')

// 常量使用
console.log('\n5. 常量配置:')
console.log('Default config:', DEFAULT_CONFIG)

// 枚举使用
console.log('\n6. 枚举:')
console.log('Status values:', Object.values(Status))

// 高阶函数使用
console.log('\n7. 防抖函数:')
const debouncedLog = debounce((message) => {
  console.log('Debounced:', message)
}, 300)

// 快速调用多次，只有最后一次会执行
debouncedLog('First call')
debouncedLog('Second call')
debouncedLog('Third call')

console.log('\n=== Example completed ===')
