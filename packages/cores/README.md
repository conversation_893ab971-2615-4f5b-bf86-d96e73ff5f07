# Cores

Core utilities for VueUse project - TypeScript library built with Rollup.

## 功能特性

- ✅ TypeScript 支持
- ✅ 多种模块格式 (ES Module, CommonJS, UMD)
- ✅ 源码映射 (Source Maps)
- ✅ 类型声明文件
- ✅ 开发模式热重载

## 安装依赖

```bash
# 如果你有 pnpm
pnpm install

# 或者使用 npm
npm install
```

## 构建

```bash
# 构建生产版本
npm run build

# 开发模式 (监听文件变化)
npm run dev

# 清理构建文件
npm run clean
```

## 输出文件

构建后会在 `dist/` 目录生成以下文件：

- `index.js` - CommonJS 格式
- `index.esm.js` - ES Module 格式  
- `index.umd.js` - UMD 格式 (可在浏览器中使用)
- `index.d.ts` - TypeScript 类型声明
- `*.map` - 源码映射文件

## 使用示例

### ES Module

```javascript
import { hello, Logger, createArray } from 'cores'

console.log(hello('World'))

const logger = new Logger('[APP]')
logger.info('Application started')

const numbers = createArray(5, 0)
console.log(numbers) // [0, 0, 0, 0, 0]
```

### CommonJS

```javascript
const { hello, Logger } = require('cores')

console.log(hello('Node.js'))
```

### 浏览器 (UMD)

```html
<script src="./dist/index.umd.js"></script>
<script>
  console.log(Cores.hello('Browser'))
  const logger = new Cores.Logger('[WEB]')
  logger.info('Page loaded')
</script>
```

## API 文档

### 函数

- `hello(name: string): string` - 返回问候语
- `createArray<T>(length: number, value: T): T[]` - 创建指定长度和值的数组
- `fetchUser(id: number): Promise<User>` - 模拟获取用户信息
- `debounce<T>(func: T, wait: number)` - 防抖函数

### 类

- `Logger` - 日志工具类
  - `info(message: string)` - 输出信息日志
  - `warn(message: string)` - 输出警告日志  
  - `error(message: string)` - 输出错误日志

### 类型

- `User` - 用户信息接口
- `UserRole` - 用户角色类型
- `Status` - 状态枚举

### 常量

- `version` - 库版本号
- `DEFAULT_CONFIG` - 默认配置对象

## 运行示例

```bash
node example.js
```
