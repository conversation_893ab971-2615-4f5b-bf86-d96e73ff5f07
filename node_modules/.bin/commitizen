#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Desktop/ALLLearn/Project/VueUseProject/node_modules/.pnpm/commitizen@4.3.1_@types+node@24.0.3_typescript@5.8.3/node_modules/commitizen/bin/node_modules:/Users/<USER>/Desktop/ALLLearn/Project/VueUseProject/node_modules/.pnpm/commitizen@4.3.1_@types+node@24.0.3_typescript@5.8.3/node_modules/commitizen/node_modules:/Users/<USER>/Desktop/ALLLearn/Project/VueUseProject/node_modules/.pnpm/commitizen@4.3.1_@types+node@24.0.3_typescript@5.8.3/node_modules:/Users/<USER>/Desktop/ALLLearn/Project/VueUseProject/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Desktop/ALLLearn/Project/VueUseProject/node_modules/.pnpm/commitizen@4.3.1_@types+node@24.0.3_typescript@5.8.3/node_modules/commitizen/bin/node_modules:/Users/<USER>/Desktop/ALLLearn/Project/VueUseProject/node_modules/.pnpm/commitizen@4.3.1_@types+node@24.0.3_typescript@5.8.3/node_modules/commitizen/node_modules:/Users/<USER>/Desktop/ALLLearn/Project/VueUseProject/node_modules/.pnpm/commitizen@4.3.1_@types+node@24.0.3_typescript@5.8.3/node_modules:/Users/<USER>/Desktop/ALLLearn/Project/VueUseProject/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../commitizen/bin/commitizen" "$@"
else
  exec node  "$basedir/../commitizen/bin/commitizen" "$@"
fi
