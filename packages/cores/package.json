{"name": "cores", "type": "module", "version": "1.0.0", "packageManager": "pnpm@10.12.1", "description": "Core utilities for VueUse project", "author": "", "license": "ISC", "keywords": [], "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "rollup -c", "dev": "rollup -c -w", "clean": "rm -rf dist", "test": "echo \"Error: no test specified\" && exit 1"}, "devDependencies": {"@rollup/plugin-commonjs": "^26.0.3", "@rollup/plugin-node-resolve": "^15.3.1", "@rollup/plugin-typescript": "^12.1.2", "rollup": "^4.44.0", "rollup-plugin-dts": "^6.2.1", "tslib": "^2.8.1", "typescript": "^5.8.3"}}