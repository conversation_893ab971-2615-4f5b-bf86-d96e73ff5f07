// Core utilities for VueUse project
export const version = '1.0.0'

// 基础工具函数
export function hello(name: string): string {
  return `Hello, ${name}!`
}

// 类型定义
export interface User {
  id: number
  name: string
  email?: string
}

export type UserRole = 'admin' | 'user' | 'guest'

// 泛型函数示例
export function createArray<T>(length: number, value: T): T[] {
  return Array.from({ length }, () => value)
}

// 异步函数示例
export async function fetchUser(id: number): Promise<User> {
  // 模拟 API 调用
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        id,
        name: `User ${id}`,
        email: `user${id}@example.com`,
      })
    }, 100)
  })
}

// 工具类示例
export class Logger {
  private prefix: string

  constructor(prefix: string = '[LOG]') {
    this.prefix = prefix
  }

  info(message: string): void {
    console.log(`${this.prefix} INFO: ${message}`)
  }

  error(message: string): void {
    console.error(`${this.prefix} ERROR: ${message}`)
  }

  warn(message: string): void {
    console.warn(`${this.prefix} WARN: ${message}`)
  }
}

// 常量导出
export const DEFAULT_CONFIG = {
  timeout: 5000,
  retries: 3,
  debug: false,
} as const

// 枚举示例
export enum Status {
  PENDING = 'pending',
  SUCCESS = 'success',
  ERROR = 'error',
}

// 高阶函数示例
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
): (...args: Parameters<T>) => void {
  let timeout: ReturnType<typeof setTimeout> | null = null

  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }

    timeout = setTimeout(() => {
      func(...args)
    }, wait)
  }
}
